import { isFulfilled, isRejected, Middleware, MiddlewareAPI } from '@reduxjs/toolkit'
import { v4 as uuid } from 'uuid'
import { addNotification } from '@/store/slices/notification-slice'
import { handleErrorResponseAndTranslate } from '@/shared/contexts/notifications/helper'
import { INotification } from '@/shared/types/store/notification'
import { t } from 'i18next'

const INVISIBLE_ENDPOINTS = [
  'getAuthophishingTemplatesStatistics',
  'getPermissions',
  'getUserInfo',
  'getInfo',
  'getPhishingLastCampaign',
  'getModalEmployees',
  'getEmployees',
  'logOut',
]

const longErrorTextPlaceholder = t('global_errors:something_went_wrong')

function containsIPAddress(input: string): boolean {
  const ipv4Pattern = /\b(?:\d{1,3}\.){3}\d{1,3}\b/g
  const ipv6Pattern = /\b(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}\b/g

  return ipv4Pattern.test(input) || ipv6Pattern.test(input)
}

type LoggerAction = {
  meta?: {
    arg?: { endpointName: string }
    baseQueryMeta?: { request?: { url?: string; method?: string } }
  }
  payload?: {
    data?: string | { message?: string; details?: string; detail?: string }
    notificationTip?: string
    status?: string
  }
  type: string
}

/**
 * Log a warning and show a toast!
 */
export const rtkQueryErrorLogger: Middleware = (api: MiddlewareAPI) => next => rtkAction => {
  const { dispatch } = api
  const loggerAction = rtkAction as LoggerAction

  if (
    isRejected(loggerAction) &&
    loggerAction?.meta?.baseQueryMeta &&
    !INVISIBLE_ENDPOINTS.includes(loggerAction?.meta?.arg?.endpointName || '')
  ) {
    let info

    try {
      info = JSON.parse(localStorage.getItem('common_info_v2') || '')
    } catch (error) {
      info = ''
    }

    const errorText =
      typeof loggerAction.payload?.data === 'string'
        ? loggerAction.payload.data
        : loggerAction?.payload?.data?.message

    if (errorText && containsIPAddress(errorText)) {
      return next(rtkAction)
    }

    const errorDetail = `
  ${t('global_errors:date')} - ${new Date()}
  ${t('global_errors:link_to_current_page')} - ${window.location.href}

  ${t('global_errors:user')} - ${info?.userID}
  ${t('global_errors:organization')} - ${info?.organizationID}

  ${t('global_errors:request')} - ${loggerAction?.meta?.baseQueryMeta?.request?.url}
  ${t('global_errors:method')} - ${loggerAction?.meta?.baseQueryMeta?.request?.method}
  ${t('global_errors:status')} - ${loggerAction?.payload?.status}
  ${t('global_errors:error_text')} - ${errorText}
  ${t('global_errors:error_details')} - ${(typeof loggerAction.payload?.data !== 'string' && loggerAction?.payload?.data?.details) || ''}
		`.trim()

    console.log(errorDetail)

    const message =
      (typeof loggerAction.payload?.data !== 'string' && loggerAction?.payload?.data?.details) ||
      (typeof loggerAction.payload?.data !== 'string' && loggerAction?.payload?.data?.detail) ||
      errorText ||
      ''

    const messageLimit = 1024
    const displayMessage = message.length > messageLimit ? longErrorTextPlaceholder : message

    try {
      dispatch(
        addNotification({
          id: uuid(),
          message: handleErrorResponseAndTranslate(displayMessage),
          status: 'error',
          detail: errorDetail,
        } as INotification),
      )
    } catch (notificationError) {
      console.error('Error adding notification:', notificationError)
      dispatch(
        addNotification({
          id: uuid(),
          message: longErrorTextPlaceholder,
          status: 'error',
          detail: errorDetail,
        } as INotification),
      )
    }
  }

  if (isFulfilled(loggerAction) && loggerAction?.payload?.notificationTip) {
    dispatch(
      addNotification({
        id: uuid(),
        message: loggerAction.payload?.notificationTip || '',
        status: 'success',
      }),
    )
  }

  return next(loggerAction)
}

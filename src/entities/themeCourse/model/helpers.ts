import { ThemeProgress } from './types'

export const isThemeCompelted = (theme: ThemeProgress) => {
  let isThemeCompelted = true

  if (theme.has_theory && theme.theory < 100) {
    isThemeCompelted = false
  }
  if (theme.has_quizes && theme.quiz < 100) {
    isThemeCompelted = false
  }

  return isThemeCompelted
}

export interface ChapterProgress {
  theory: number
  quiz: number
  themes?: ThemeProgress[]
}

export const isChapterCompleted = (chapter: ChapterProgress) => {
  let isChapterCompleted = true

  const hasTheory = chapter.themes?.some(theme => theme.has_theory) ?? false
  const hasQuizes = chapter.themes?.some(theme => theme.has_quizes) ?? false

  if (hasTheory && chapter.theory < 100) {
    isChapterCompleted = false
  }
  if (hasQuizes && chapter.quiz < 100) {
    isChapterCompleted = false
  }

  return isChapterCompleted
}

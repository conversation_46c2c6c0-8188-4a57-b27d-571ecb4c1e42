.page {
  display: flex;
  flex-direction: column;
  padding-bottom: 30px;
}

.searchWrapper {
  width: 100%;
  max-width: 316px;
  margin-right: auto;

  .search {
    background: #fff;
    width: 100%;
  }
}

.sort {
  margin-top: 32px;
  margin-left: auto;

  .wrapper {
    cursor: pointer;

    padding: 4px;
    position: relative;
    .text {
      align-items: center;

      color: var(--color-gray-80, #5c6585);
      display: flex;
      font: var(--font-text-2-medium);
      letter-spacing: 0.13px;
    }
  }

  .listWrapper {
    background: var(--color-surface, #fff);
    border: 1px solid var(--stroke, #ebeff2);
    border-radius: 8px;
    box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.1);
    max-width: max-content;
    overflow: -moz-scrollbars-none;
    -ms-overflow-style: none;
    overflow-y: scroll;
    padding: 8px 0;
    position: absolute;
    right: 0;
    top: 100%;
    transform: translateY(4px);

    width: max-content;
    z-index: 10;

    &::-webkit-scrollbar {
      width: 0;
    }

    .listInner {
      height: max-content;
    }
    .listItem {
      color: var(--color-gray-80, #5c6585);

      cursor: pointer;

      display: block;
      font: var(--font-text-2-normal);
      padding: 8px 16px;

      transition: var(--transition);
      &:hover {
        background: var(--color-gray-40, #f0f3f7);

        transition: var(--transition);
      }
      &.active {
        background: var(--color-gray-40, #f0f3f7);

        transition: var(--transition);
      }
    }
  }
}

.content {
  display: grid;
  margin-top: 12px;
  grid-gap: 16px;
  grid-template-columns: repeat(3, 1fr);

  @media (max-width: 1200px) {
    grid-template-columns: repeat(2, 1fr);
  }
  @media (max-width: 900px) {
    grid-template-columns: repeat(1, 1fr);
  }
}

.title {
  font: var(--font-title-2-medium);
  color: var(--color-gray-90);
}

.titleWrap {
  display: flex;
  // justify-content: space-between;
  gap: 16px;
  align-items: center;
}

.linkBtn {
  font: var(--font-text-2-normal);
  padding: 9px 25px;
}

.actions {
  align-items: center;
  background: var(--color-surface, #fff);
  border: 1px solid var(--stroke, #ebeff2);
  border-radius: 12px;
  bottom: 32px;
  box-shadow:
    0 0 88px -4px rgba(24, 39, 75, 0.12),
    0 0 28px -6px rgba(24, 39, 75, 0.12);
  display: flex;
  gap: 24px;
  max-width: min(100vw - 255px - 30px, var(--page-container) - 64px);
  padding: 14px 24px 14px 16px;
  position: fixed;
  width: 100%;
  z-index: 100;

  &__counter {
    color: var(--color-primary, #3dbc87);
    font: var(--font-text-2-normal);
    margin-right: auto;
  }

  &__element {
    align-items: center;
    color: var(--color-gray-80, #5c6585);
    cursor: pointer;
    display: flex;
    font: var(--font-text-2-normal);
    gap: 8px;

    &:hover {
      color: var(--color-primary);

      svg,
      path {
        fill: var(--color-primary) !important;
      }
    }
  }

  &__delete {
    &:hover {
      color: var(--color-error) !important;

      svg,
      path {
        fill: var(--color-error) !important;
      }
    }
  }

  &__cancel {
    color: var(--color-warn) !important;

    &:hover {
      color: var(--color-component-warn-hover) !important;

      svg,
      path {
        fill: var(--color-component-warn-hover) !important;
      }
    }
  }

  .red {
    color: var(--color-error);
  }

  &__disabled {
    opacity: 0.4;

    &:hover {
      color: var(--color-gray-80, #5c6585) !important;

      svg,
      path {
        fill: var(--color-gray-90) !important;
      }
    }
  }
}

.courseCardSkeleton {
  min-height: 274px;
}

.courseTopAdornment {
  position: absolute;
  top: 0px;
  left: 0px;
  padding: 20px;
  z-index: 1;
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;
  align-items: flex-start;
  gap: 10px;
  width: 100%;
  height: var(--tag-image-height);
}

.courseBottomAdornment {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 28px;
}

.themesCount {
  font: var(--font-caption-1-medium);
  color: var(--color-gray-80);
}

.courseActions {
  display: flex;
  gap: 4px;
}

.filterTags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

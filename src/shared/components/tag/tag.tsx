import { FC, ReactNode } from 'react'
import styles from './tag.module.scss'
import classNamesBind from 'classnames/bind'
import { TagProps } from './tag.d'
import { ButtonIcon } from '@/shared/ui'
import useEmblaCarousel from 'embla-carousel-react'

const cx = classNamesBind.bind(styles)

export const Tag: FC<TagProps.Props> = props => {
  const { className, info, isActive = false, onDelete, onClick } = props
  const { title, color } = info

  const styles = isActive
    ? {
      background: color,
      border: `1px solid ${color}`,
      color: '#fff',
    }
    : {
      background: 'transparent',
      border: `1px solid ${color}`,
      color: color,
    }

  return (
    <div className={cx('wrapper', className)} style={styles} onClick={onClick}>
      <div className={cx('textWrapper')}>{title}</div>
      {onDelete && (
        <ButtonIcon
          size='16'
          iconSize='16'
          icon='closeBold'
          type='button'
          onClick={e => {
            e.stopPropagation()
            onDelete()
          }}
        />
      )}
    </div>
  )
}

export const CourseCardTagsSlider = ({ children }: { children: ReactNode }) => {
  const [emblaRef] = useEmblaCarousel(
    {
      dragFree: true,
    },
    [],
  )

  return (
    <div ref={emblaRef} className={cx('courseTagsWrapper')}>
      <div className={cx('courseTags')}>{children}</div>
    </div>
  )
}
